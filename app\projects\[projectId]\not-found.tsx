import Container from "@/components/shared/container/container";
import { <PERSON><PERSON> } from "@heroui/react";
import Link from "next/link";

export default function NotFound() {
    return (
        <Container className="!p-8 items-center justify-center min-h-[50vh]" orientation="vertical">
            <div className="text-center space-y-4">
                <h1 className="text-4xl font-bold text-gray-900">404</h1>
                <h2 className="text-2xl font-semibold text-gray-700">Project Not Found</h2>
                <p className="text-gray-600 max-w-md">
                    The project you're looking for doesn't exist or may have been removed.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-6">
                    <Button 
                        as={Link} 
                        href="/projects" 
                        color="primary"
                        className="w-full sm:w-auto"
                    >
                        Back to Projects
                    </Button>
                    <Button 
                        as={Link} 
                        href="/" 
                        variant="bordered"
                        className="w-full sm:w-auto"
                    >
                        Go Home
                    </Button>
                </div>
            </div>
        </Container>
    );
}
