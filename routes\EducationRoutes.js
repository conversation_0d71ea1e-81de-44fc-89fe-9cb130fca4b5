const express = require('express');
const router = express.Router();

const EducationController = require('../controllers/EducationController');
const { accessTokenChecker } = require("../middlewares/token-middlewares");
const { requireRole } = require("../middlewares/role-checker");

router.get('/', EducationController.getListEducation);
router.post("/", accessToken<PERSON>hecker, requireRole(1), EducationController.addNewEducation);
router.get("/:educationId", EducationController.getEducationDetails);
router.patch("/:educationId", accessToken<PERSON>hecker, requireRole(1), EducationController.updateEducationDetails);
router.delete("/:educationId", accessTokenChecker, requireRole(1), EducationController.permanentDeleteEducation);
router.patch("/:educationId/delete", accessToken<PERSON>hecker, requireRole(1), EducationController.softDeleteEducation);
router.patch("/:educationId/recover", accessToken<PERSON>he<PERSON>, requireRole(1), EducationController.recoverEducation);

module.exports = router;